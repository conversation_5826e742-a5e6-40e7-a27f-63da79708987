# 通知功能SQL语法修复说明

## 问题描述

在深入排查会诊申请创建后受邀者没有收到提示弹窗的问题时，发现了一个关键问题：

**PersistentNotificationMapper.xml中使用了SQL Server语法，但应用配置的是MySQL数据库，导致SQL执行失败。**

## 问题根源

### 1. 数据库配置
应用配置文件`application-dev.yml`中配置的是MySQL数据库：
```yaml
datasource:
  driverClassName: com.mysql.cj.jdbc.Driver
  url: **********************************?...
```

### 2. SQL语法不匹配
但是`PersistentNotificationMapper.xml`中使用的是SQL Server语法：
- `GETDATE()` - SQL Server的当前时间函数
- `top(#{limit})` - SQL Server的限制查询语法
- `DATEADD()` - SQL Server的日期计算函数

### 3. 错误影响
这导致所有涉及持久化通知的SQL操作都会失败，包括：
- 创建通知记录
- 查询待发送通知
- 更新通知状态
- 重试机制

## 修复方案

### SQL Server语法 → MySQL语法对照表

| SQL Server | MySQL | 说明 |
|------------|-------|------|
| `GETDATE()` | `NOW()` | 获取当前时间 |
| `select top(#{limit})` | `select ... limit #{limit}` | 限制查询结果数量 |
| `DATEADD(MINUTE, n, GETDATE())` | `DATE_ADD(NOW(), INTERVAL n MINUTE)` | 日期加法 |
| `DATEADD(DAY, -n, GETDATE())` | `DATE_SUB(NOW(), INTERVAL n DAY)` | 日期减法 |

### 具体修复内容

#### 1. 查询语句修复
```xml
<!-- 修复前 (SQL Server) -->
<select id="selectRetryNotifications">
    where next_retry_time <= GETDATE()
</select>

<!-- 修复后 (MySQL) -->
<select id="selectRetryNotifications">
    where next_retry_time <= NOW()
</select>
```

#### 2. 限制查询修复
```xml
<!-- 修复前 (SQL Server) -->
<select id="selectHighPriorityNotifications">
    select top(#{limit}) id, user_id, ...
    from persistent_notification
</select>

<!-- 修复后 (MySQL) -->
<select id="selectHighPriorityNotifications">
    <include refid="selectPersistentNotificationVo"/>
    where status = 'PENDING' and priority >= 3
    order by priority desc, create_time asc
    limit #{limit}
</select>
```

#### 3. 插入语句修复
```xml
<!-- 修复前 (SQL Server) -->
<insert id="insertPersistentNotification">
    values (..., GETDATE())
</insert>

<!-- 修复后 (MySQL) -->
<insert id="insertPersistentNotification">
    values (..., NOW())
</insert>
```

#### 4. 更新语句修复
```xml
<!-- 修复前 (SQL Server) -->
<update id="updateNotificationStatus">
    set send_time = GETDATE(),
        update_time = GETDATE()
</update>

<!-- 修复后 (MySQL) -->
<update id="updateNotificationStatus">
    set send_time = NOW(),
        update_time = NOW()
</update>
```

#### 5. 日期计算修复
```xml
<!-- 修复前 (SQL Server) -->
<update id="incrementRetryCount">
    set next_retry_time = DATEADD(MINUTE, retry_count * 2, GETDATE())
</update>

<!-- 修复后 (MySQL) -->
<update id="incrementRetryCount">
    set next_retry_time = DATE_ADD(NOW(), INTERVAL retry_count * 2 MINUTE)
</update>
```

#### 6. 删除过期数据修复
```xml
<!-- 修复前 (SQL Server) -->
<delete id="deleteExpiredNotifications">
    where delivery_time < DATEADD(DAY, -#{retentionDays}, GETDATE())
</delete>

<!-- 修复后 (MySQL) -->
<delete id="deleteExpiredNotifications">
    where delivery_time < DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
</delete>
```

## 验证方法

### 1. 检查数据库表
确保`persistent_notification`表已创建：
```sql
SHOW TABLES LIKE '%notification%';
DESCRIBE persistent_notification;
```

### 2. 测试SQL语句
可以直接在MySQL中测试修复后的SQL语句：
```sql
-- 测试插入
INSERT INTO persistent_notification (user_id, title, content, status, create_time) 
VALUES (1, '测试通知', '测试内容', 'PENDING', NOW());

-- 测试查询
SELECT * FROM persistent_notification WHERE create_time >= NOW() - INTERVAL 1 DAY;
```

### 3. 应用测试
1. 重启应用
2. 创建会诊申请
3. 检查数据库中是否有通知记录
4. 观察前端是否收到通知

## 注意事项

1. **数据库一致性**：确保所有SQL语句都使用与配置数据库匹配的语法
2. **时区问题**：MySQL的`NOW()`函数返回的是服务器时区的时间
3. **性能考虑**：MySQL的`LIMIT`语法比SQL Server的`TOP`更灵活
4. **兼容性**：如果需要支持多种数据库，建议使用MyBatis的动态SQL或数据库方言

## 相关文件

- `ruoyi-admin/src/main/resources/mapper/consultation/PersistentNotificationMapper.xml`
- `ruoyi-admin/src/main/resources/application-dev.yml`
- `sql/create_persistent_notification_table.sql`

## 总结

这个SQL语法不匹配的问题是导致通知功能完全失效的根本原因。修复后，通知系统应该能够正常工作：

1. 会诊申请创建时能够正确保存通知记录
2. SSE连接能够正常发送通知
3. 重试机制能够正常工作
4. 前端能够收到通知弹窗

这个问题提醒我们在开发过程中要注意数据库语法的一致性，特别是在多数据库环境下。
