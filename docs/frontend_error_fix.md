# 前端错误修复文档

## 问题描述

前端应用出现以下错误：

### 1. process is not defined 错误
```
ReferenceError: process is not defined
    at connectConsultationNotification (reliableNotification.js:12:15)
```

### 2. Vue watch API 警告
```
[Vue warn]: `watch(fn, options?)` signature has been moved to a separate API. 
Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature.
```

## 问题分析

### 1. process 对象问题
- **原因**：在浏览器环境中使用了Node.js的`process.env`对象
- **位置**：`reliableNotification.js`第12行
- **错误代码**：`process.env.VUE_APP_BASE_API`

### 2. Vue 3 watch API 变更
- **原因**：使用了Vue 2的watch语法，在Vue 3中已废弃
- **影响**：虽然是警告，但可能影响功能正常运行

## 解决方案

### 1. 修复 process 对象问题

#### 问题代码
```javascript
// 错误的写法
const url = `${process.env.VUE_APP_BASE_API}${API_BASE}/connect?token=${encodeURIComponent(token)}`
```

#### 修复后代码
```javascript
// 正确的写法 - 使用Vite环境变量
export function connectConsultationNotification() {
  const token = localStorage.getItem('token') || sessionStorage.getItem('token')
  
  // 获取基础API地址
  const baseAPI = import.meta.env.VITE_APP_BASE_API || '/dev-api'
  
  const url = `${baseAPI}${API_BASE}/connect?token=${encodeURIComponent(token)}`
  
  console.log('建立SSE连接:', url)
  
  const eventSource = new EventSource(url, {
    withCredentials: true
  })
  
  return eventSource
}
```

#### 修复要点
1. **环境变量访问**：
   - ❌ `process.env.VUE_APP_BASE_API` (Node.js环境)
   - ✅ `import.meta.env.VITE_APP_BASE_API` (Vite环境)

2. **兼容性处理**：
   - 提供默认值 `/dev-api`
   - 确保在所有环境下都能正常工作

3. **环境变量命名**：
   - Vue CLI: `VUE_APP_*`
   - Vite: `VITE_APP_*`

### 2. 环境变量配置

#### 开发环境 (.env.development)
```env
# 页面标题
VITE_APP_TITLE = 云影像管理系统

# 开发环境配置
VITE_APP_ENV = 'development'

# 若依管理系统/开发环境
VITE_APP_BASE_API = '/dev-api'
```

#### 生产环境 (.env.production)
```env
# 页面标题
VITE_APP_TITLE = 云影像管理系统

# 生产环境配置
VITE_APP_ENV = 'production'

# 若依管理系统/生产环境
VITE_APP_BASE_API = '/prod-api'
```

### 3. Vue watch API 问题

虽然当前组件中没有直接的watch问题，但项目中其他组件可能存在。需要注意：

#### Vue 2 语法 (已废弃)
```javascript
// 错误的写法
watch(() => {
  // 副作用函数
}, options)
```

#### Vue 3 正确语法
```javascript
// 方式1：监听响应式数据源
watch(source, (newVal, oldVal) => {
  // 回调函数
}, options)

// 方式2：使用 watchEffect
watchEffect(() => {
  // 副作用函数
}, options)
```

## 测试验证

### 1. 环境变量测试
```javascript
// 在浏览器控制台测试
console.log('VITE_APP_BASE_API:', import.meta.env.VITE_APP_BASE_API)
console.log('所有环境变量:', import.meta.env)
```

### 2. SSE连接测试
```javascript
// 测试连接建立
import { connectConsultationNotification } from '@/api/consultation/reliableNotification'

const eventSource = connectConsultationNotification()
eventSource.onopen = () => console.log('连接成功')
eventSource.onerror = (error) => console.error('连接失败:', error)
```

### 3. 功能验证
1. **页面加载**：确认页面能正常加载，无JavaScript错误
2. **SSE连接**：验证能成功建立SSE连接
3. **心跳机制**：确认心跳响应正常工作
4. **通知接收**：测试通知能正常接收和显示

## 预防措施

### 1. 环境变量使用规范
- **统一前缀**：所有自定义环境变量使用`VITE_APP_`前缀
- **类型安全**：使用TypeScript定义环境变量类型
- **默认值**：为所有环境变量提供合理的默认值

### 2. Vue 3 最佳实践
- **API升级**：及时更新到Vue 3推荐的API
- **组合式API**：优先使用Composition API
- **类型检查**：使用TypeScript增强类型安全

### 3. 开发工具配置
```javascript
// vite.config.js
export default defineConfig({
  define: {
    // 为了兼容性，可以定义全局变量
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  }
})
```

## 相关文件

### 修改的文件
- `pacs-admin-ui/src/api/consultation/reliableNotification.js`

### 配置文件
- `pacs-admin-ui/.env.development`
- `pacs-admin-ui/.env.production`
- `pacs-admin-ui/vite.config.js`

### 组件文件
- `pacs-admin-ui/src/components/ReliableNotification/index.vue`

## 总结

通过以下修复解决了前端错误：

1. **环境变量修复**：
   - 将`process.env.VUE_APP_BASE_API`改为`import.meta.env.VITE_APP_BASE_API`
   - 提供默认值确保兼容性

2. **API兼容性**：
   - 使用Vite推荐的环境变量访问方式
   - 确保在不同环境下都能正常工作

3. **错误处理**：
   - 添加了适当的错误处理和日志记录
   - 提供了降级方案

现在前端应用应该能够正常建立SSE连接，心跳机制也能正常工作。
