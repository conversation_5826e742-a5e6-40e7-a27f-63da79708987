#!/bin/bash

# 会诊通知流程调试脚本
# 用于深入排查会诊申请创建后受邀者没有收到提示弹窗的问题

# 配置参数
BASE_URL="http://localhost:8080"
API_BASE="/consultation/reliable-notification"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 检查数据库表是否存在
check_database_tables() {
    log_info "=== 检查数据库表 ==="
    
    # 检查persistent_notification表
    log_debug "检查persistent_notification表..."
    
    # 这里需要根据实际数据库配置修改
    # 示例：使用MySQL命令行工具
    # mysql -u username -p database_name -e "DESCRIBE persistent_notification;"
    
    log_warn "请手动检查以下数据库表是否存在："
    echo "1. persistent_notification - 持久化通知表"
    echo "2. consultation_request - 会诊申请表"
    echo "3. sys_user - 用户表"
    echo ""
    echo "检查命令示例："
    echo "mysql -u username -p database_name -e \"SHOW TABLES LIKE '%notification%';\""
    echo "mysql -u username -p database_name -e \"DESCRIBE persistent_notification;\""
}

# 检查后端服务状态
check_backend_services() {
    log_info "=== 检查后端服务状态 ==="
    
    # 检查应用是否启动
    log_debug "检查应用启动状态..."
    if curl -s "${BASE_URL}/actuator/health" > /dev/null 2>&1; then
        log_info "✓ 应用服务正常运行"
    else
        log_error "✗ 应用服务无法访问"
        return 1
    fi
    
    # 检查SSE连接端点
    log_debug "检查SSE连接端点..."
    response=$(curl -s -o /dev/null -w "%{http_code}" "${BASE_URL}${API_BASE}/connect")
    if [ "$response" = "401" ]; then
        log_info "✓ SSE端点存在（需要认证）"
    elif [ "$response" = "200" ]; then
        log_info "✓ SSE端点正常"
    else
        log_error "✗ SSE端点异常，HTTP状态码: $response"
    fi
}

# 检查前端文件
check_frontend_files() {
    log_info "=== 检查前端文件 ==="
    
    local files=(
        "pacs-admin-ui/src/components/ReliableNotification/index.vue"
        "pacs-admin-ui/src/api/consultation/reliableNotification.js"
        "pacs-admin-ui/src/layout/index.vue"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            log_info "✓ $file"
        else
            log_error "✗ $file (文件不存在)"
        fi
    done
    
    # 检查layout集成
    if [ -f "pacs-admin-ui/src/layout/index.vue" ]; then
        if grep -q "ReliableNotification" "pacs-admin-ui/src/layout/index.vue"; then
            log_info "✓ ReliableNotification组件已集成到layout"
        else
            log_error "✗ ReliableNotification组件未集成到layout"
        fi
    fi
}

# 检查日志文件
check_application_logs() {
    log_info "=== 检查应用日志 ==="
    
    log_debug "查找关键日志信息..."
    
    # 查找最近的日志文件
    local log_patterns=(
        "logs/spring.log"
        "logs/application.log"
        "/var/log/your-app/application.log"
        "nohup.out"
    )
    
    local found_log=false
    
    for pattern in "${log_patterns[@]}"; do
        if [ -f "$pattern" ]; then
            log_info "发现日志文件: $pattern"
            found_log=true
            
            # 检查关键日志
            log_debug "检查SSE连接日志..."
            if grep -q "SSE连接" "$pattern" 2>/dev/null; then
                log_info "✓ 发现SSE连接相关日志"
                echo "最近的SSE连接日志："
                grep "SSE连接" "$pattern" | tail -5
            else
                log_warn "✗ 未发现SSE连接相关日志"
            fi
            
            log_debug "检查通知发送日志..."
            if grep -q "异步发送会诊申请通知" "$pattern" 2>/dev/null; then
                log_info "✓ 发现通知发送相关日志"
                echo "最近的通知发送日志："
                grep "异步发送会诊申请通知" "$pattern" | tail -5
            else
                log_warn "✗ 未发现通知发送相关日志"
            fi
            
            log_debug "检查错误日志..."
            if grep -q "ERROR" "$pattern" 2>/dev/null; then
                echo "最近的错误日志："
                grep "ERROR" "$pattern" | tail -10
            fi
            
            break
        fi
    done
    
    if [ "$found_log" = false ]; then
        log_warn "未找到应用日志文件，请检查以下位置："
        for pattern in "${log_patterns[@]}"; do
            echo "  - $pattern"
        done
    fi
}

# 模拟创建会诊申请
simulate_consultation_request() {
    log_info "=== 模拟会诊申请创建 ==="
    
    log_warn "这是一个模拟测试，需要有效的认证token"
    log_debug "请在浏览器中："
    echo "1. 登录系统"
    echo "2. 打开开发者工具的Network标签"
    echo "3. 创建一个会诊申请"
    echo "4. 观察以下请求："
    echo "   - POST /consultation/request (创建申请)"
    echo "   - GET ${API_BASE}/connect (SSE连接)"
    echo "   - 查看Console中的日志输出"
}

# 检查配置项
check_system_config() {
    log_info "=== 检查系统配置 ==="
    
    log_debug "需要检查的配置项："
    echo "1. notification.max.retry.count - 通知最大重试次数"
    echo "2. notification.ack.timeout.seconds - 通知确认超时时间"
    echo "3. notification.persistence.enabled - 启用通知持久化"
    echo ""
    echo "检查命令示例："
    echo "mysql -u username -p database_name -e \"SELECT * FROM sys_config WHERE config_key LIKE '%notification%';\""
}

# 生成调试报告
generate_debug_report() {
    log_info "=== 生成调试报告 ==="
    
    local report_file="notification_debug_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "会诊通知调试报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "1. 系统环境"
        echo "操作系统: $(uname -a)"
        echo "Java版本: $(java -version 2>&1 | head -1)"
        echo ""
        
        echo "2. 应用状态"
        echo "应用URL: $BASE_URL"
        echo "SSE端点: ${BASE_URL}${API_BASE}/connect"
        echo ""
        
        echo "3. 检查清单"
        echo "□ 数据库表persistent_notification已创建"
        echo "□ ReliableNotification组件已集成到layout"
        echo "□ SSE连接端点正常响应"
        echo "□ 应用日志中有通知发送记录"
        echo "□ 前端控制台无错误信息"
        echo "□ 浏览器Network标签显示SSE连接"
        echo ""
        
        echo "4. 排查步骤"
        echo "1. 检查数据库表是否存在"
        echo "2. 检查应用日志中的错误信息"
        echo "3. 检查前端控制台的错误信息"
        echo "4. 检查SSE连接是否建立成功"
        echo "5. 检查用户是否有权限接收通知"
        echo ""
        
        echo "5. 常见问题"
        echo "- Token为null导致认证失败"
        echo "- 数据库表不存在"
        echo "- 前端组件未正确集成"
        echo "- SSE连接被防火墙阻止"
        echo "- 异步配置未生效"
        
    } > "$report_file"
    
    log_info "调试报告已生成: $report_file"
}

# 主函数
main() {
    echo "========================================"
    echo "会诊通知流程深度调试工具"
    echo "========================================"
    echo ""
    
    check_database_tables
    echo ""
    
    check_backend_services
    echo ""
    
    check_frontend_files
    echo ""
    
    check_application_logs
    echo ""
    
    check_system_config
    echo ""
    
    simulate_consultation_request
    echo ""
    
    generate_debug_report
    echo ""
    
    log_info "调试完成！请根据上述检查结果进行问题排查。"
}

# 执行主函数
main "$@"
