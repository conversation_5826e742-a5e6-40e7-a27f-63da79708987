<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.consultation.mapper.PersistentNotificationMapper">
    
    <resultMap type="PersistentNotification" id="PersistentNotificationResult">
        <result property="id"                    column="id"                    />
        <result property="userId"                column="user_id"               />
        <result property="consultationId"        column="consultation_id"       />
        <result property="notificationType"      column="notification_type"     />
        <result property="title"                 column="title"                 />
        <result property="content"               column="content"               />
        <result property="messageData"           column="message_data"          />
        <result property="status"                column="status"                />
        <result property="retryCount"            column="retry_count"           />
        <result property="maxRetryCount"         column="max_retry_count"       />
        <result property="nextRetryTime"         column="next_retry_time"       />
        <result property="sendTime"              column="send_time"             />
        <result property="deliveryTime"          column="delivery_time"         />
        <result property="ackTime"               column="ack_time"              />
        <result property="expireTime"            column="expire_time"           />
        <result property="errorMessage"          column="error_message"         />
        <result property="priority"              column="priority"              />
        <result property="requireAck"            column="require_ack"           />
        <result property="deviceType"            column="device_type"           />
        <result property="userAgent"             column="user_agent"            />
        <result property="ipAddress"             column="ip_address"            />
        <result property="createBy"              column="create_by"             />
        <result property="createTime"            column="create_time"           />
        <result property="updateBy"              column="update_by"             />
        <result property="updateTime"            column="update_time"           />
        <result property="remark"                column="remark"                />
    </resultMap>

    <sql id="selectPersistentNotificationVo">
        select id, user_id, consultation_id, notification_type, title, content, message_data, 
               status, retry_count, max_retry_count, next_retry_time, send_time, delivery_time, 
               ack_time, expire_time, error_message, priority, require_ack, device_type, 
               user_agent, ip_address, create_by, create_time, update_by, update_time, remark 
        from persistent_notification
    </sql>

    <select id="selectPersistentNotificationList" parameterType="PersistentNotification" resultMap="PersistentNotificationResult">
        <include refid="selectPersistentNotificationVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="consultationId != null "> and consultation_id = #{consultationId}</if>
            <if test="notificationType != null  and notificationType != ''"> and notification_type = #{notificationType}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="priority != null "> and priority = #{priority}</if>
            <if test="requireAck != null "> and require_ack = #{requireAck}</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
        </where>
        order by priority desc, create_time desc
    </select>
    
    <select id="selectPersistentNotificationById" parameterType="Long" resultMap="PersistentNotificationResult">
        <include refid="selectPersistentNotificationVo"/>
        where id = #{id}
    </select>

    <select id="selectPendingNotificationsByUserId" parameterType="Long" resultMap="PersistentNotificationResult">
        <include refid="selectPersistentNotificationVo"/>
        where user_id = #{userId} and status = 'PENDING'
        order by priority desc, create_time asc
    </select>

    <select id="selectSentNotifications" resultMap="PersistentNotificationResult">
        <include refid="selectPersistentNotificationVo"/>
        where status = 'SENT' and require_ack = 1
        order by send_time asc
    </select>

    <select id="selectRetryNotifications" resultMap="PersistentNotificationResult">
        <include refid="selectPersistentNotificationVo"/>
        where status = 'PENDING'
        and retry_count &lt; max_retry_count
        and next_retry_time &lt;= NOW()
        order by priority desc, next_retry_time asc
    </select>

    <select id="selectHighPriorityNotifications" parameterType="int" resultMap="PersistentNotificationResult">
        <include refid="selectPersistentNotificationVo"/>
        where status = 'PENDING' and priority >= 3
        order by priority desc, create_time asc
        limit #{limit}
    </select>

    <select id="selectRecentNotificationsByUserId" resultMap="PersistentNotificationResult">
        <include refid="selectPersistentNotificationVo"/>
        where user_id = #{userId}
        order by create_time desc
        limit #{limit}
    </select>

    <select id="selectFailedNotificationsInHours" parameterType="int" resultMap="PersistentNotificationResult">
        <include refid="selectPersistentNotificationVo"/>
        where status = 'FAILED' 
        and create_time >= DATEADD(HOUR, -#{hours}, GETDATE())
        order by create_time desc
    </select>

    <select id="selectNotificationStatistics" parameterType="Long" resultType="java.util.Map">
        select 
            count(*) as total_notifications,
            count(case when status = 'PENDING' then 1 end) as pending_count,
            count(case when status = 'SENT' then 1 end) as sent_count,
            count(case when status = 'DELIVERED' then 1 end) as delivered_count,
            count(case when status = 'FAILED' then 1 end) as failed_count,
            avg(cast(retry_count as float)) as avg_retry_count,
            max(create_time) as last_notification_time,
            case 
                when count(case when status = 'DELIVERED' then 1 end) > 0 
                then cast(count(case when status = 'DELIVERED' then 1 end) as float) / count(*) * 100
                else 0 
            end as delivery_rate
        from persistent_notification
        where user_id = #{userId}
    </select>

    <select id="selectNotificationSendStatistics" resultType="java.util.Map">
        select 
            count(*) as total_sent,
            count(case when status = 'DELIVERED' then 1 end) as delivered_count,
            count(case when status = 'FAILED' then 1 end) as failed_count,
            avg(cast(retry_count as float)) as avg_retry_count,
            case 
                when count(*) > 0 
                then cast(count(case when status = 'DELIVERED' then 1 end) as float) / count(*) * 100
                else 0 
            end as success_rate
        from persistent_notification
        where create_time between #{startTime} and #{endTime}
    </select>
        
    <insert id="insertPersistentNotification" parameterType="PersistentNotification" useGeneratedKeys="true" keyProperty="id">
        insert into persistent_notification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="consultationId != null">consultation_id,</if>
            <if test="notificationType != null and notificationType != ''">notification_type,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null">content,</if>
            <if test="messageData != null">message_data,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="retryCount != null">retry_count,</if>
            <if test="maxRetryCount != null">max_retry_count,</if>
            <if test="nextRetryTime != null">next_retry_time,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="deliveryTime != null">delivery_time,</if>
            <if test="ackTime != null">ack_time,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="priority != null">priority,</if>
            <if test="requireAck != null">require_ack,</if>
            <if test="deviceType != null and deviceType != ''">device_type,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="ipAddress != null and ipAddress != ''">ip_address,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="consultationId != null">#{consultationId},</if>
            <if test="notificationType != null and notificationType != ''">#{notificationType},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="messageData != null">#{messageData},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="retryCount != null">#{retryCount},</if>
            <if test="maxRetryCount != null">#{maxRetryCount},</if>
            <if test="nextRetryTime != null">#{nextRetryTime},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="deliveryTime != null">#{deliveryTime},</if>
            <if test="ackTime != null">#{ackTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="priority != null">#{priority},</if>
            <if test="requireAck != null">#{requireAck},</if>
            <if test="deviceType != null and deviceType != ''">#{deviceType},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="ipAddress != null and ipAddress != ''">#{ipAddress},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            NOW()
        </trim>
    </insert>

    <update id="updatePersistentNotification" parameterType="PersistentNotification">
        update persistent_notification
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="consultationId != null">consultation_id = #{consultationId},</if>
            <if test="notificationType != null and notificationType != ''">notification_type = #{notificationType},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="messageData != null">message_data = #{messageData},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="retryCount != null">retry_count = #{retryCount},</if>
            <if test="maxRetryCount != null">max_retry_count = #{maxRetryCount},</if>
            <if test="nextRetryTime != null">next_retry_time = #{nextRetryTime},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="deliveryTime != null">delivery_time = #{deliveryTime},</if>
            <if test="ackTime != null">ack_time = #{ackTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="requireAck != null">require_ack = #{requireAck},</if>
            <if test="deviceType != null and deviceType != ''">device_type = #{deviceType},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="ipAddress != null and ipAddress != ''">ip_address = #{ipAddress},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = NOW()
        </trim>
        where id = #{id}
    </update>

    <update id="updateNotificationStatus">
        update persistent_notification
        set status = #{status},
            <if test="status == 'SENT'">send_time = NOW(),</if>
            <if test="status == 'DELIVERED'">delivery_time = NOW(), ack_time = NOW(),</if>
            <if test="status == 'FAILED'">error_message = #{errorMessage},</if>
            update_time = NOW()
        where id = #{id}
    </update>

    <update id="incrementRetryCount">
        update persistent_notification
        set retry_count = retry_count + 1,
            next_retry_time = DATE_ADD(NOW(), INTERVAL retry_count * 2 MINUTE),
            update_time = NOW()
        where id = #{id}
    </update>

    <update id="markAsDelivered">
        update persistent_notification
        set status = 'DELIVERED',
            delivery_time = NOW(),
            ack_time = NOW(),
            update_time = NOW()
        where id = #{id}
    </update>

    <update id="batchUpdateNotificationStatus">
        update persistent_notification
        set status = #{status},
            update_time = NOW()
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deletePersistentNotificationById" parameterType="Long">
        delete from persistent_notification where id = #{id}
    </delete>

    <delete id="deletePersistentNotificationByIds" parameterType="String">
        delete from persistent_notification where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteExpiredNotifications" parameterType="int">
        delete from persistent_notification
        where status = 'DELIVERED'
        and delivery_time &lt; DATE_SUB(NOW(), INTERVAL #{retentionDays} DAY)
    </delete>

    <select id="selectUndeliveredNotificationsByUserId" parameterType="Long" resultMap="PersistentNotificationResult">
        <include refid="selectPersistentNotificationVo"/>
        where user_id = #{userId}
        and status in ('PENDING', 'SENT')
        order by priority desc, create_time desc
    </select>

    <update id="clearUserNotifications">
        update persistent_notification
        set status = 'CLEARED',
            update_time = NOW()
        where user_id = #{userId}
        and status in ('PENDING', 'SENT')
    </update>

</mapper>
