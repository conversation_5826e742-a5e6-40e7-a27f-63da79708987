package com.ruoyi.consultation.service;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.consultation.domain.ConsultationRequest;
import com.ruoyi.consultation.domain.PersistentNotification;
import com.ruoyi.consultation.mapper.PersistentNotificationMapper;
import com.ruoyi.consultation.message.ConsultationNotificationMessage;
import com.ruoyi.consultation.service.SseConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 可靠通知服务
 * 确保会诊通知100%送达
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@Slf4j
public class ReliableNotificationService {

    @Autowired
    private PersistentNotificationMapper persistentNotificationMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SseConnectionService sseConnectionService;

    private static final String USER_ONLINE_KEY = "consultation:user:online:";
    private static final String NOTIFICATION_QUEUE_KEY = "consultation:notification:queue:";
    private static final String NOTIFICATION_ACK_KEY = "consultation:notification:ack:";

    /**
     * 发送可靠通知
     * 
     * @param userId 用户ID
     * @param message 通知消息
     * @param consultationId 会诊ID（可选）
     * @return 通知ID
     */
    public Long sendReliableNotification(Long userId, ConsultationNotificationMessage message, Long consultationId) {
        try {
            // 1. 持久化通知到数据库
            PersistentNotification notification = new PersistentNotification();
            notification.setUserId(userId);
            notification.setConsultationId(consultationId);
            notification.setNotificationType(message.getType());
            notification.setTitle(message.getTitle());
            notification.setContent(message.getContent());
            notification.setMessageData(JSON.toJSONString(message));
            notification.setStatus("PENDING");
            notification.setCreateTime(new Date());
            notification.setRetryCount(0);
            notification.setMaxRetryCount(10);
            notification.setNextRetryTime(new Date());

            persistentNotificationMapper.insertPersistentNotification(notification);
            Long notificationId = notification.getId();

            log.info("持久化通知已创建，通知ID: {}, 用户ID: {}", notificationId, userId);

            // 2. 尝试实时发送
            boolean sent = attemptRealTimeSend(userId, message, notificationId);
            
            if (sent) {
                // 实时发送成功，等待用户确认
                scheduleAckCheck(notificationId, userId);
            } else {
                // 实时发送失败，加入离线队列
                addToOfflineQueue(userId, notificationId);
            }

            return notificationId;

        } catch (Exception e) {
            log.error("发送可靠通知失败，用户ID: {}", userId, e);
            return null;
        }
    }

    /**
     * 尝试实时发送通知
     */
    private boolean attemptRealTimeSend(Long userId, ConsultationNotificationMessage message, Long notificationId) {
        try {
            // 添加通知ID到消息中，用于确认
            message.setNotificationId(notificationId);
            
            // 通过SSE发送
            boolean sseResult = sseConnectionService.sendNotificationToUser(userId, message);
            
            if (sseResult) {
                log.info("SSE实时通知发送成功，通知ID: {}, 用户ID: {}", notificationId, userId);
                
                // 更新发送状态
                persistentNotificationMapper.updateNotificationStatus(notificationId, "SENT", null);
                
                // 记录用户在线状态
                markUserOnline(userId);
                
                return true;
            } else {
                log.warn("SSE实时通知发送失败，用户可能离线，通知ID: {}, 用户ID: {}", notificationId, userId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("实时发送通知异常，通知ID: {}, 用户ID: {}", notificationId, userId, e);
            return false;
        }
    }

    /**
     * 添加到离线队列
     */
    private void addToOfflineQueue(Long userId, Long notificationId) {
        try {
            String queueKey = NOTIFICATION_QUEUE_KEY + userId;
            redisCache.setCacheList(queueKey, List.of(notificationId));
            // 设置7天过期时间
            redisCache.expire(queueKey, 7, TimeUnit.DAYS);
            
            log.info("通知已加入离线队列，通知ID: {}, 用户ID: {}", notificationId, userId);
            
        } catch (Exception e) {
            log.error("添加离线队列失败，通知ID: {}, 用户ID: {}", notificationId, userId, e);
        }
    }

    /**
     * 安排确认检查
     */
    private void scheduleAckCheck(Long notificationId, Long userId) {
        // 30秒后检查是否收到确认
        String ackKey = NOTIFICATION_ACK_KEY + notificationId;
        redisCache.setCacheObject(ackKey, "WAITING", 30, TimeUnit.SECONDS);
        
        log.debug("已安排确认检查，通知ID: {}, 用户ID: {}", notificationId, userId);
    }

    /**
     * 确认通知已收到
     */
    public boolean acknowledgeNotification(Long notificationId, Long userId) {
        try {
            String ackKey = NOTIFICATION_ACK_KEY + notificationId;
            
            if (redisCache.hasKey(ackKey)) {
                // 标记为已确认
                redisCache.setCacheObject(ackKey, "ACKNOWLEDGED", 1, TimeUnit.HOURS);
                
                // 更新数据库状态
                persistentNotificationMapper.updateNotificationStatus(notificationId, "DELIVERED", null);
                
                log.info("通知确认成功，通知ID: {}, 用户ID: {}", notificationId, userId);
                return true;
            } else {
                log.warn("通知确认失败，可能已过期，通知ID: {}, 用户ID: {}", notificationId, userId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("确认通知异常，通知ID: {}, 用户ID: {}", notificationId, userId, e);
            return false;
        }
    }

    /**
     * 用户上线时处理离线通知
     */
    public void handleUserOnline(Long userId) {
        try {
            log.info("用户上线，处理离线通知，用户ID: {}", userId);
            
            // 标记用户在线
            markUserOnline(userId);
            
            // 处理离线队列中的通知
            String queueKey = NOTIFICATION_QUEUE_KEY + userId;
            List<Object> offlineNotifications = redisCache.getCacheList(queueKey);
            
            if (offlineNotifications != null && !offlineNotifications.isEmpty()) {
                log.info("发现 {} 条离线通知，用户ID: {}", offlineNotifications.size(), userId);
                
                for (Object notificationIdObj : offlineNotifications) {
                    Long notificationId = Long.valueOf(notificationIdObj.toString());
                    processOfflineNotification(userId, notificationId);
                }
                
                // 清空离线队列
                redisCache.deleteObject(queueKey);
            }
            
            // 处理数据库中未送达的通知
            List<PersistentNotification> pendingNotifications = 
                persistentNotificationMapper.selectPendingNotificationsByUserId(userId);
                
            for (PersistentNotification notification : pendingNotifications) {
                processOfflineNotification(userId, notification.getId());
            }
            
        } catch (Exception e) {
            log.error("处理用户上线异常，用户ID: {}", userId, e);
        }
    }

    /**
     * 处理离线通知
     */
    private void processOfflineNotification(Long userId, Long notificationId) {
        try {
            PersistentNotification notification = 
                persistentNotificationMapper.selectPersistentNotificationById(notificationId);
                
            if (notification == null || !"PENDING".equals(notification.getStatus())) {
                return;
            }
            
            // 重新构建消息
            ConsultationNotificationMessage message = 
                JSON.parseObject(notification.getMessageData(), ConsultationNotificationMessage.class);
            message.setNotificationId(notificationId);
            
            // 尝试发送
            boolean sent = attemptRealTimeSend(userId, message, notificationId);
            
            if (sent) {
                scheduleAckCheck(notificationId, userId);
            } else {
                // 增加重试次数
                persistentNotificationMapper.incrementRetryCount(notificationId);
            }
            
        } catch (Exception e) {
            log.error("处理离线通知异常，通知ID: {}, 用户ID: {}", notificationId, userId, e);
        }
    }

    /**
     * 标记用户在线
     */
    private void markUserOnline(Long userId) {
        String onlineKey = USER_ONLINE_KEY + userId;
        redisCache.setCacheObject(onlineKey, System.currentTimeMillis(), 5, TimeUnit.MINUTES);
    }

    /**
     * 检查用户是否在线
     */
    public boolean isUserOnline(Long userId) {
        String onlineKey = USER_ONLINE_KEY + userId;
        return redisCache.hasKey(onlineKey);
    }

    /**
     * 定时任务：处理未确认的通知
     */
    @Scheduled(fixedDelay = 30000) // 每30秒执行一次
    public void processUnacknowledgedNotifications() {
        try {
            // 查找已发送但未确认的通知
            List<PersistentNotification> sentNotifications = 
                persistentNotificationMapper.selectSentNotifications();
                
            for (PersistentNotification notification : sentNotifications) {
                String ackKey = NOTIFICATION_ACK_KEY + notification.getId();
                
                if (!redisCache.hasKey(ackKey)) {
                    // 确认已过期，重新发送
                    log.warn("通知确认超时，重新发送，通知ID: {}, 用户ID: {}", 
                        notification.getId(), notification.getUserId());
                        
                    if (notification.getRetryCount() < notification.getMaxRetryCount()) {
                        processOfflineNotification(notification.getUserId(), notification.getId());
                    } else {
                        // 超过最大重试次数，标记为失败
                        persistentNotificationMapper.updateNotificationStatus(
                            notification.getId(), "FAILED", "超过最大重试次数");
                        log.error("通知发送失败，超过最大重试次数，通知ID: {}", notification.getId());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("处理未确认通知异常", e);
        }
    }

    /**
     * 定时任务：清理过期通知
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupExpiredNotifications() {
        try {
            // 清理7天前的已送达通知
            int cleanedCount = persistentNotificationMapper.deleteExpiredNotifications(7);
            log.info("清理过期通知完成，清理数量: {}", cleanedCount);

        } catch (Exception e) {
            log.error("清理过期通知异常", e);
        }
    }

    /**
     * 清理过期通知（手动调用）
     */
    public void cleanupExpiredNotifications(int retentionDays) {
        try {
            int cleanedCount = persistentNotificationMapper.deleteExpiredNotifications(retentionDays);
            log.info("手动清理过期通知完成，保留天数: {}, 清理数量: {}", retentionDays, cleanedCount);
        } catch (Exception e) {
            log.error("手动清理过期通知异常", e);
        }
    }

    /**
     * 获取通知列表
     */
    public List<PersistentNotification> getNotificationList(PersistentNotification notification) {
        return persistentNotificationMapper.selectPersistentNotificationList(notification);
    }

    /**
     * 获取通知统计信息
     */
    public java.util.Map<String, Object> getNotificationStatistics(Long userId) {
        return persistentNotificationMapper.selectNotificationStatistics(userId);
    }

    /**
     * 获取在线用户ID集合
     */
    public java.util.Set<Long> getOnlineUserIds() {
        return sseConnectionService.getOnlineUserIds();
    }

    /**
     * 重发通知
     */
    public boolean resendNotification(Long notificationId) {
        try {
            PersistentNotification notification =
                persistentNotificationMapper.selectPersistentNotificationById(notificationId);

            if (notification == null) {
                log.error("通知不存在，通知ID: {}", notificationId);
                return false;
            }

            if (!"FAILED".equals(notification.getStatus()) && !"PENDING".equals(notification.getStatus())) {
                log.warn("通知状态不允许重发，通知ID: {}, 状态: {}", notificationId, notification.getStatus());
                return false;
            }

            // 重新构建消息
            ConsultationNotificationMessage message =
                com.alibaba.fastjson2.JSON.parseObject(notification.getMessageData(), ConsultationNotificationMessage.class);
            message.setNotificationId(notificationId);

            // 重置状态为待发送
            persistentNotificationMapper.updateNotificationStatus(notificationId, "PENDING", null);

            // 尝试发送
            boolean sent = attemptRealTimeSend(notification.getUserId(), message, notificationId);

            if (sent) {
                scheduleAckCheck(notificationId, notification.getUserId());
                log.info("重发通知成功，通知ID: {}", notificationId);
                return true;
            } else {
                // 增加重试次数
                persistentNotificationMapper.incrementRetryCount(notificationId);
                log.warn("重发通知失败，通知ID: {}", notificationId);
                return false;
            }

        } catch (Exception e) {
            log.error("重发通知异常，通知ID: {}", notificationId, e);
            return false;
        }
    }

    /**
     * 发送离线通知给用户
     * 当用户连接时调用，发送所有离线通知
     */
    public void sendOfflineNotificationsToUser(Long userId) {
        try {
            log.info("开始发送离线通知给用户: {}", userId);

            // 标记用户在线
            markUserOnline(userId);

            // 处理离线队列中的通知
            String queueKey = NOTIFICATION_QUEUE_KEY + userId;
            List<Object> offlineNotifications = redisCache.getCacheList(queueKey);

            if (offlineNotifications != null && !offlineNotifications.isEmpty()) {
                log.info("发现Redis离线队列中有 {} 条通知，用户ID: {}", offlineNotifications.size(), userId);

                for (Object notificationIdObj : offlineNotifications) {
                    try {
                        Long notificationId = Long.valueOf(notificationIdObj.toString());
                        processOfflineNotification(userId, notificationId);
                    } catch (Exception e) {
                        log.error("处理Redis离线通知失败，通知ID: {}, 用户ID: {}", notificationIdObj, userId, e);
                    }
                }

                // 清空离线队列
                redisCache.deleteObject(queueKey);
                log.info("已清空Redis离线队列，用户ID: {}", userId);
            }

            // 处理数据库中未送达的通知
            List<PersistentNotification> pendingNotifications =
                persistentNotificationMapper.selectPendingNotificationsByUserId(userId);

            if (pendingNotifications != null && !pendingNotifications.isEmpty()) {
                log.info("发现数据库中有 {} 条待发送通知，用户ID: {}", pendingNotifications.size(), userId);

                for (PersistentNotification notification : pendingNotifications) {
                    try {
                        processOfflineNotification(userId, notification.getId());
                    } catch (Exception e) {
                        log.error("处理数据库离线通知失败，通知ID: {}, 用户ID: {}", notification.getId(), userId, e);
                    }
                }
            }

            log.info("离线通知发送完成，用户ID: {}", userId);

        } catch (Exception e) {
            log.error("发送离线通知异常，用户ID: {}", userId, e);
        }
    }

    /**
     * 获取用户的离线通知列表
     */
    public List<PersistentNotification> getOfflineNotifications(Long userId) {
        try {
            log.info("获取用户离线通知列表，用户ID: {}", userId);

            // 获取数据库中的待发送和已发送但未确认的通知
            List<PersistentNotification> notifications =
                persistentNotificationMapper.selectUndeliveredNotificationsByUserId(userId);

            log.info("找到 {} 条离线通知，用户ID: {}", notifications.size(), userId);
            return notifications;

        } catch (Exception e) {
            log.error("获取离线通知列表异常，用户ID: {}", userId, e);
            return List.of();
        }
    }

    /**
     * 批量确认通知
     */
    public int batchAcknowledgeNotifications(Long userId, List<Long> notificationIds) {
        try {
            int successCount = 0;

            for (Long notificationId : notificationIds) {
                if (acknowledgeNotification(notificationId, userId)) {
                    successCount++;
                }
            }

            log.info("批量确认通知完成，用户ID: {}, 成功: {}/{}", userId, successCount, notificationIds.size());
            return successCount;

        } catch (Exception e) {
            log.error("批量确认通知异常，用户ID: {}", userId, e);
            return 0;
        }
    }

    /**
     * 清理用户的所有通知
     */
    public void clearUserNotifications(Long userId) {
        try {
            log.info("清理用户所有通知，用户ID: {}", userId);

            // 清理Redis离线队列
            String queueKey = NOTIFICATION_QUEUE_KEY + userId;
            redisCache.deleteObject(queueKey);

            // 清理Redis确认键
            // 注意：这里需要扫描所有相关的确认键，实际实现中可能需要优化

            // 更新数据库中的通知状态为已清理
            persistentNotificationMapper.clearUserNotifications(userId);

            log.info("用户通知清理完成，用户ID: {}", userId);

        } catch (Exception e) {
            log.error("清理用户通知异常，用户ID: {}", userId, e);
        }
    }

    /**
     * 标记用户的所有通知为已送达
     */
    public int markUserNotificationsAsDelivered(Long userId) {
        try {
            // 查询用户的所有SENT状态通知
            List<PersistentNotification> sentNotifications = persistentNotificationMapper
                .selectSentNotifications().stream()
                .filter(n -> n.getUserId().equals(userId))
                .collect(Collectors.toList());

            int count = 0;
            for (PersistentNotification notification : sentNotifications) {
                persistentNotificationMapper.markAsDelivered(notification.getId());
                count++;
            }

            log.info("标记用户 {} 的 {} 条通知为已送达", userId, count);
            return count;

        } catch (Exception e) {
            log.error("标记用户通知为已送达失败，用户ID: {}", userId, e);
            return 0;
        }
    }
}
