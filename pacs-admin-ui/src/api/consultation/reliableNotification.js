import request from '@/utils/request'
import { getToken } from '@/utils/auth'

// 可靠通知API基础路径
const API_BASE = '/consultation/reliable-notification'

/**
 * 建立SSE连接
 * @returns {EventSource} SSE连接对象
 */
export function connectConsultationNotification() {
  const token = getToken()

  // 获取基础API地址
  const baseAPI = import.meta.env.VITE_APP_BASE_API || '/dev-api'

  const url = `${baseAPI}${API_BASE}/connect?token=${encodeURIComponent(token || '')}`

  console.log('建立SSE连接:', url)

  const eventSource = new EventSource(url, {
    withCredentials: true
  })

  return eventSource
}

/**
 * 发送心跳响应
 * @returns {Promise}
 */
export function sendHeartbeatResponse() {
  return request({
    url: `${API_BASE}/heartbeat`,
    method: 'post'
  })
}

/**
 * 确认通知已收到
 * @param {number} notificationId 通知ID
 * @returns {Promise}
 */
export function acknowledgeNotification(notificationId) {
  return request({
    url: `${API_BASE}/acknowledge/${notificationId}`,
    method: 'post'
  })
}

/**
 * 获取离线通知
 * @returns {Promise}
 */
export function getOfflineNotifications() {
  return request({
    url: `${API_BASE}/offline`,
    method: 'get'
  })
}

/**
 * 获取用户通知列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getNotificationList(params) {
  return request({
    url: `${API_BASE}/list`,
    method: 'get',
    params
  })
}

/**
 * 标记通知为已读
 * @param {number} notificationId 通知ID
 * @returns {Promise}
 */
export function markNotificationAsRead(notificationId) {
  return request({
    url: `${API_BASE}/mark-read/${notificationId}`,
    method: 'post'
  })
}

/**
 * 批量标记通知为已读
 * @param {Array} notificationIds 通知ID数组
 * @returns {Promise}
 */
export function batchMarkNotificationsAsRead(notificationIds) {
  return request({
    url: `${API_BASE}/batch-mark-read`,
    method: 'post',
    data: { notificationIds }
  })
}

/**
 * 删除通知
 * @param {number} notificationId 通知ID
 * @returns {Promise}
 */
export function deleteNotification(notificationId) {
  return request({
    url: `${API_BASE}/${notificationId}`,
    method: 'delete'
  })
}

/**
 * 批量删除通知
 * @param {Array} notificationIds 通知ID数组
 * @returns {Promise}
 */
export function batchDeleteNotifications(notificationIds) {
  return request({
    url: `${API_BASE}/batch-delete`,
    method: 'delete',
    data: { notificationIds }
  })
}

/**
 * 获取通知统计信息
 * @returns {Promise}
 */
export function getNotificationStatistics() {
  return request({
    url: `${API_BASE}/statistics`,
    method: 'get'
  })
}

/**
 * 获取通知设置
 * @returns {Promise}
 */
export function getNotificationSettings() {
  return request({
    url: `${API_BASE}/settings`,
    method: 'get'
  })
}

/**
 * 更新通知设置
 * @param {Object} settings 设置对象
 * @returns {Promise}
 */
export function updateNotificationSettings(settings) {
  return request({
    url: `${API_BASE}/settings`,
    method: 'put',
    data: settings
  })
}

/**
 * 测试通知发送
 * @param {Object} testData 测试数据
 * @returns {Promise}
 */
export function testNotificationSend(testData) {
  return request({
    url: `${API_BASE}/test`,
    method: 'post',
    data: testData
  })
}

/**
 * 重发失败的通知
 * @param {number} notificationId 通知ID
 * @returns {Promise}
 */
export function resendNotification(notificationId) {
  return request({
    url: `${API_BASE}/resend/${notificationId}`,
    method: 'post'
  })
}

/**
 * 获取通知发送日志
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getNotificationLogs(params) {
  return request({
    url: `${API_BASE}/logs`,
    method: 'get',
    params
  })
}

/**
 * 检查连接状态
 * @returns {Promise}
 */
export function checkConnectionStatus() {
  return request({
    url: `${API_BASE}/status`,
    method: 'get'
  })
}

/**
 * 手动触发离线通知处理
 * @returns {Promise}
 */
export function triggerOfflineNotificationProcess() {
  return request({
    url: `${API_BASE}/process-offline`,
    method: 'post'
  })
}

/**
 * 获取通知模板
 * @returns {Promise}
 */
export function getNotificationTemplates() {
  return request({
    url: `${API_BASE}/templates`,
    method: 'get'
  })
}

/**
 * 发送自定义通知
 * @param {Object} notificationData 通知数据
 * @returns {Promise}
 */
export function sendCustomNotification(notificationData) {
  return request({
    url: `${API_BASE}/send-custom`,
    method: 'post',
    data: notificationData
  })
}

/**
 * 获取用户在线状态
 * @param {number} userId 用户ID
 * @returns {Promise}
 */
export function getUserOnlineStatus(userId) {
  return request({
    url: `${API_BASE}/online-status/${userId}`,
    method: 'get'
  })
}

/**
 * 获取系统通知配置
 * @returns {Promise}
 */
export function getSystemNotificationConfig() {
  return request({
    url: `${API_BASE}/system-config`,
    method: 'get'
  })
}

/**
 * 更新系统通知配置
 * @param {Object} config 配置对象
 * @returns {Promise}
 */
export function updateSystemNotificationConfig(config) {
  return request({
    url: `${API_BASE}/system-config`,
    method: 'put',
    data: config
  })
}

/**
 * 导出通知数据
 * @param {Object} params 导出参数
 * @returns {Promise}
 */
export function exportNotificationData(params) {
  return request({
    url: `${API_BASE}/export`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 清理过期通知
 * @param {number} retentionDays 保留天数
 * @returns {Promise}
 */
export function cleanupExpiredNotifications(retentionDays) {
  return request({
    url: `${API_BASE}/cleanup`,
    method: 'post',
    data: { retentionDays }
  })
}
