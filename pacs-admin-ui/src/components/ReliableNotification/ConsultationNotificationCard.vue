<template>
  <div class="consultation-notification-card">
    <!-- 通知头部 -->
    <div class="notification-header">
      <div class="notification-icon">
        <el-icon :size="24" :color="getIconColor(notification.type)">
          <component :is="getIconComponent(notification.type)" />
        </el-icon>
      </div>
      <div class="notification-title">
        <h4>{{ notification.title }}</h4>
        <span class="notification-time">{{ formatTime(notification.createTime) }}</span>
      </div>
      <div class="notification-close">
        <el-button
          type="text"
          :icon="Close"
          size="small"
          @click="handleClose"
        />
      </div>
    </div>

    <!-- 通知内容 -->
    <div class="notification-content">
      <div class="consultation-info">
        <div class="info-row" v-if="notification.requestNo">
          <span class="label">申请编号：</span>
          <span class="value">{{ notification.requestNo }}</span>
        </div>
        
        <div class="info-row" v-if="notification.requesterName">
          <span class="label">申请医生：</span>
          <span class="value">{{ notification.requesterName }}</span>
        </div>
        
        <div class="info-row" v-if="notification.patientName">
          <span class="label">患者姓名：</span>
          <span class="value">{{ notification.patientName }}</span>
        </div>
        
        <div class="info-row" v-if="notification.urgencyLevel">
          <span class="label">紧急程度：</span>
          <el-tag 
            :type="getUrgencyTagType(notification.urgencyLevel)" 
            size="small"
          >
            {{ getUrgencyText(notification.urgencyLevel) }}
          </el-tag>
        </div>
        
        <div class="info-row" v-if="notification.content">
          <span class="label">详细说明：</span>
          <span class="value description">{{ notification.content }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="notification-actions">
      <el-button 
        type="primary" 
        size="small" 
        @click="handleViewDetails"
        :icon="View"
      >
        查看详情
      </el-button>
      
      <el-button 
        v-if="notification.type === 'REQUEST'" 
        type="success" 
        size="small" 
        @click="handleQuickAccept"
        :icon="Check"
      >
        快速接受
      </el-button>
      
      <el-button
        size="small"
        @click="handleClose"
        :icon="Close"
      >
        关闭
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Close, 
  View, 
  Check, 
  User, 
  Document, 
  Warning, 
  SuccessFilled,
  InfoFilled 
} from '@element-plus/icons-vue'

const props = defineProps({
  notification: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'action'])

const router = useRouter()

// 获取图标组件
const getIconComponent = (type) => {
  const iconMap = {
    'REQUEST': User,
    'ACCEPT': SuccessFilled,
    'REJECT': Warning,
    'COMPLETE': Check,
    'CANCEL': Close,
    'URGENT': Warning
  }
  return iconMap[type] || InfoFilled
}

// 获取图标颜色
const getIconColor = (type) => {
  const colorMap = {
    'REQUEST': '#409EFF',
    'ACCEPT': '#67C23A',
    'REJECT': '#F56C6C',
    'COMPLETE': '#67C23A',
    'CANCEL': '#909399',
    'URGENT': '#E6A23C'
  }
  return colorMap[type] || '#409EFF'
}

// 获取紧急程度标签类型
const getUrgencyTagType = (urgencyLevel) => {
  const typeMap = {
    'URGENT': 'danger',
    'HIGH': 'warning',
    'NORMAL': 'info',
    'LOW': 'info'
  }
  return typeMap[urgencyLevel] || 'info'
}

// 获取紧急程度文本
const getUrgencyText = (urgencyLevel) => {
  const textMap = {
    'URGENT': '紧急',
    'HIGH': '高',
    'NORMAL': '普通',
    'LOW': '低'
  }
  return textMap[urgencyLevel] || '普通'
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

// 查看详情
const handleViewDetails = () => {
  if (props.notification.url) {
    router.push(props.notification.url)
  } else if (props.notification.requestNo) {
    router.push(`/consultation/detail/${props.notification.requestNo}`)
  } else {
    router.push('/consultation/my')
  }
  emit('close')
}

// 快速接受
const handleQuickAccept = () => {
  emit('action', {
    type: 'accept',
    notification: props.notification
  })
  ElMessage.success('会诊申请已接受')
  emit('close')
}

// 关闭通知
const handleClose = () => {
  console.log('🔔 关闭通知卡片')
  emit('close')
}
</script>

<style scoped>
.consultation-notification-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 16px;
  min-width: 320px;
  max-width: 400px;
  border-left: 4px solid #409EFF;
}

.notification-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.notification-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.notification-title {
  flex: 1;
}

.notification-title h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-close {
  flex-shrink: 0;
}

.notification-content {
  margin-bottom: 16px;
}

.consultation-info {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #303133;
  flex: 1;
}

.description {
  line-height: 1.5;
}

.notification-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 紧急通知样式 */
.consultation-notification-card[data-urgent="true"] {
  border-left-color: #E6A23C;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
  }
  100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}
</style>
