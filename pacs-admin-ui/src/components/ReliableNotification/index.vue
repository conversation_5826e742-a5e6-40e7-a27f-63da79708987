<template>
  <div class="reliable-notification">
    <!-- 连接状态指示器 -->
    <div v-if="showConnectionStatus" class="connection-status" :class="connectionStatusClass">
      <el-icon><Connection /></el-icon>
      <span>{{ connectionStatusText }}</span>
    </div>

    <!-- 通知弹窗通过JavaScript动态创建，不在模板中显示 -->

    <!-- 离线通知提示 -->
    <el-dialog
      v-model="showOfflineDialog"
      title="离线通知"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="offline-notifications">
        <p>您有 {{ offlineNotifications.length }} 条离线通知：</p>
        <div class="notification-list">
          <div
            v-for="notification in offlineNotifications"
            :key="notification.id"
            class="notification-item"
          >
            <div class="notification-header">
              <span class="notification-title">{{ notification.title }}</span>
              <span class="notification-time">{{ formatTime(notification.createTime) }}</span>
            </div>
            <div class="notification-content">{{ notification.content }}</div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="markAllAsRead">全部标记为已读</el-button>
          <el-button type="primary" @click="showOfflineDialog = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style>
/* 自定义通知样式 */
.consultation-notification .el-notification__content {
  padding: 0 !important;
  margin: 0 !important;
}

.consultation-notification .el-notification__closeBtn {
  display: none !important;
}

.consultation-notification {
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}
</style>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, h } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { Connection } from '@element-plus/icons-vue'
import { connectConsultationNotification, acknowledgeNotification, getOfflineNotifications, sendHeartbeatResponse } from '@/api/consultation/reliableNotification'
import { getToken } from '@/utils/auth'
import ConsultationNotificationCard from './ConsultationNotificationCard.vue'

// 响应式数据
const eventSource = ref(null)
const isConnected = ref(false)
const reconnectAttempts = ref(0)
const maxReconnectAttempts = ref(10)
const reconnectInterval = ref(null)
const activeNotifications = ref([])
const offlineNotifications = ref([])
const showOfflineDialog = ref(false)
const showConnectionStatus = ref(false)
const heartbeatInterval = ref(null)
const lastHeartbeat = ref(Date.now())
const connectionCheckInterval = ref(null)

// 配置
const config = reactive({
  reconnectDelay: 1000, // 初始重连延迟
  maxReconnectDelay: 30000, // 最大重连延迟
  heartbeatInterval: 30000, // 心跳间隔（与后端保持一致）
  heartbeatTimeout: 90000, // 心跳超时时间（与后端保持一致，允许3次心跳失败）
  notificationTimeout: 0, // 通知显示时间（0表示不自动关闭）
  connectionCheckInterval: 60000, // 每分钟检查一次连接状态
  ackTimeout: 30000, // 确认超时时间
  enableSound: true, // 启用声音提醒
  enableVibration: true // 启用震动提醒（移动端）
})

// 计算属性
const connectionStatusClass = computed(() => ({
  'status-connected': isConnected.value,
  'status-disconnected': !isConnected.value,
  'status-reconnecting': reconnectAttempts.value > 0 && !isConnected.value
}))

const connectionStatusText = computed(() => {
  if (isConnected.value) {
    return '通知连接正常'
  } else if (reconnectAttempts.value > 0) {
    return `正在重连... (${reconnectAttempts.value}/${maxReconnectAttempts.value})`
  } else {
    return '通知连接断开'
  }
})

// 连接到通知服务
const connectToNotificationService = async () => {
  try {
    console.log('🔔 ReliableNotification组件：建立可靠通知连接...')

    // 检查token是否存在
    const token = getToken()
    if (!token) {
      console.warn('🔔 用户未登录，无法建立通知连接')
      return
    }

    console.log('🔔 Token存在，创建SSE连接...')
    eventSource.value = connectConsultationNotification()
    console.log('🔔 SSE连接对象已创建:', eventSource.value)

    // 连接成功事件
    eventSource.value.addEventListener('connected', handleConnected)

    // 接收通知
    eventSource.value.addEventListener('consultation-notification', handleNotification)
    console.log('🔔 已注册consultation-notification事件监听器')

    // 接收广播通知
    eventSource.value.addEventListener('consultation-broadcast', handleBroadcast)

    // 心跳响应
    eventSource.value.addEventListener('heartbeat', handleHeartbeat)

    // 离线通知
    eventSource.value.addEventListener('offline-notifications', handleOfflineNotifications)

    // 连接错误处理
    eventSource.value.onerror = handleConnectionError

    // 连接打开事件
    eventSource.value.onopen = handleConnectionOpen

  } catch (error) {
    console.error('🔔 建立通知连接失败:', error)
    ElMessage.error('建立通知连接失败')
    scheduleReconnect()
  }
}

// 处理连接成功
const handleConnected = (event) => {
  console.log('通知连接已建立:', event.data)
  isConnected.value = true
  reconnectAttempts.value = 0
  showConnectionStatus.value = false
  
  // 启动心跳
  startHeartbeat()
  
  // 请求离线通知
  requestOfflineNotifications()
}

// 处理通知
const handleNotification = (event) => {
  try {
    const notification = JSON.parse(event.data)
    console.log('🔔 ReliableNotification组件收到通知:', notification)

    // 显示通知
    console.log('🔔 准备显示Element Plus通知...')
    showNotification(notification)
    console.log('🔔 Element Plus通知已调用')

    // 发送确认
    if (notification.notificationId) {
      acknowledgeNotificationReceived(notification.notificationId)
      console.log('🔔 发送通知确认，通知ID:', notification.notificationId)
    }

    // 播放提示音
    if (config.enableSound) {
      playNotificationSound()
    }

    // 震动提醒（移动端）
    if (config.enableVibration && 'vibrate' in navigator) {
      navigator.vibrate([200, 100, 200])
    }

  } catch (error) {
    console.error('处理通知失败:', error)
  }
}

// 处理广播通知
const handleBroadcast = (event) => {
  try {
    const notification = JSON.parse(event.data)
    console.log('收到广播通知:', notification)
    showNotification(notification)
  } catch (error) {
    console.error('处理广播通知失败:', error)
  }
}

// 处理心跳
const handleHeartbeat = (event) => {
  try {
    lastHeartbeat.value = Date.now()
    console.debug('收到心跳:', event.data)

    // 发送心跳响应给服务端
    sendHeartbeatResponseToServer()

  } catch (error) {
    console.error('处理心跳失败:', error)
  }
}

// 发送心跳响应给服务端
const sendHeartbeatResponseToServer = async () => {
  try {
    await sendHeartbeatResponse()
    console.debug('心跳响应发送成功')
  } catch (error) {
    console.warn('心跳响应发送失败:', error)
    // 心跳响应失败不影响主要功能，只记录警告
  }
}

// 处理离线通知
const handleOfflineNotifications = (event) => {
  try {
    const notifications = JSON.parse(event.data)
    if (notifications && notifications.length > 0) {
      offlineNotifications.value = notifications
      showOfflineDialog.value = true
      console.log('收到离线通知:', notifications.length, '条')
    }
  } catch (error) {
    console.error('处理离线通知失败:', error)
  }
}

// 处理连接错误
const handleConnectionError = (error) => {
  console.error('通知连接错误:', error)
  isConnected.value = false
  showConnectionStatus.value = true

  // 检查是否是认证错误
  if (error.target && error.target.readyState === EventSource.CLOSED) {
    const token = getToken()
    if (!token) {
      console.warn('用户未登录，停止重连')
      ElMessage.warning('请先登录后再使用通知功能')
      return
    }
  }

  if (eventSource.value && eventSource.value.readyState === EventSource.CLOSED) {
    console.log('连接已关闭，尝试重连...')
    scheduleReconnect()
  }
}

// 处理连接打开
const handleConnectionOpen = (event) => {
  console.log('通知连接已打开:', event)
  isConnected.value = true
}

// 显示通知
const showNotification = (notification) => {
  console.log('🔔 showNotification被调用，通知数据:', notification)

  // 确保必要字段不为空
  const title = notification.title || '新通知'
  const content = notification.content || notification.message || '您有新的通知'

  // 使用自定义通知组件
  const notificationConfig = {
    id: notification.notificationId || Date.now(),
    title: title,
    message: h(ConsultationNotificationCard, {
      notification: notification,
      onClose: () => handleNotificationClose(notification),
      onAction: (action) => handleNotificationAction(action)
    }),
    type: 'info',
    duration: 0, // 不自动关闭
    position: 'bottom-right',
    showClose: false, // 使用自定义关闭按钮
    dangerouslyUseHTMLString: false,
    customClass: 'consultation-notification'
  }

  console.log('🔔 Element Plus通知配置:', notificationConfig)

  // 使用Element Plus的通知组件
  try {
    const notificationInstance = ElNotification(notificationConfig)
    console.log('🔔 Element Plus通知实例:', notificationInstance)

    // 保存通知实例以便后续操作
    notification._instance = notificationInstance

  } catch (error) {
    console.error('🔔 Element Plus通知显示失败:', error)
    // 如果自定义通知失败，使用简单通知
    ElNotification({
      title: title,
      message: content,
      type: getNotificationType(notification.type),
      duration: config.notificationTimeout,
      position: 'bottom-right',
      onClick: () => handleNotificationClick(notification)
    })
  }

  // 添加到活动通知列表
  activeNotifications.value.push(notification)
  console.log('🔔 活动通知列表:', activeNotifications.value)
}

// 获取通知类型
const getNotificationType = (type) => {
  const typeMap = {
    'REQUEST': 'info',
    'ACCEPT': 'success',
    'REJECT': 'warning',
    'COMPLETE': 'success',
    'CANCEL': 'warning',
    'URGENT': 'error'
  }
  return typeMap[type] || 'info'
}

// 处理通知点击
const handleNotificationClick = (notification) => {
  console.log('通知被点击:', notification)
  // 这里可以添加跳转逻辑
  if (notification.url) {
    window.open(notification.url, '_blank')
  }
}

// 处理通知关闭
const handleNotificationClose = (notification) => {
  // 关闭Element Plus通知实例
  if (notification._instance) {
    notification._instance.close()
  }

  // 从活动通知列表中移除
  const index = activeNotifications.value.findIndex(n =>
    n.notificationId === notification.notificationId || n.id === notification.id
  )
  if (index > -1) {
    activeNotifications.value.splice(index, 1)
  }
}

// 处理通知操作
const handleNotificationAction = async (action) => {
  console.log('🔔 处理通知操作:', action)

  try {
    if (action.type === 'accept') {
      // 这里可以调用接受会诊的API
      // await acceptConsultationRequest(action.notification.requestNo)
      ElMessage.success('会诊申请已接受')
    }
    // 可以添加其他操作类型
  } catch (error) {
    console.error('处理通知操作失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

// 确认通知已收到
const acknowledgeNotificationReceived = async (notificationId) => {
  try {
    console.log('🔔 开始确认通知:', notificationId)
    const response = await acknowledgeNotification(notificationId)
    console.log('🔔 通知确认成功:', notificationId, response)
  } catch (error) {
    console.error('🔔 通知确认失败:', notificationId, error)
    // 确认失败不影响通知显示，只记录错误
    if (error.response) {
      console.error('🔔 确认失败详情:', error.response.status, error.response.data)
    }
  }
}

// 请求离线通知
const requestOfflineNotifications = async () => {
  try {
    const response = await getOfflineNotifications()
    if (response.data && response.data.length > 0) {
      offlineNotifications.value = response.data
      showOfflineDialog.value = true
    }
  } catch (error) {
    console.error('获取离线通知失败:', error)
  }
}

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    for (const notification of offlineNotifications.value) {
      if (notification.notificationId) {
        await acknowledgeNotificationReceived(notification.notificationId)
      }
    }
    offlineNotifications.value = []
    showOfflineDialog.value = false
    ElMessage.success('所有通知已标记为已读')
  } catch (error) {
    console.error('标记通知为已读失败:', error)
    ElMessage.error('标记通知为已读失败')
  }
}

// 安排重连
const scheduleReconnect = () => {
  // 检查token是否存在
  const token = getToken()
  if (!token) {
    console.warn('用户未登录，停止重连')
    return
  }

  if (reconnectAttempts.value >= maxReconnectAttempts.value) {
    console.error('达到最大重连次数，停止重连')
    ElMessage.error('通知连接失败，请刷新页面重试')
    return
  }

  const delay = Math.min(
    config.reconnectDelay * Math.pow(2, reconnectAttempts.value),
    config.maxReconnectDelay
  )

  reconnectAttempts.value++
  console.log(`${delay}ms后进行第${reconnectAttempts.value}次重连`)

  reconnectInterval.value = setTimeout(() => {
    connectToNotificationService()
  }, delay)
}

// 启动心跳
const startHeartbeat = () => {
  if (heartbeatInterval.value) {
    clearInterval(heartbeatInterval.value)
  }
  
  heartbeatInterval.value = setInterval(() => {
    const now = Date.now()
    if (now - lastHeartbeat.value > config.heartbeatTimeout) {
      console.warn('心跳超时，连接可能已断开')
      isConnected.value = false
      scheduleReconnect()
    }
  }, config.heartbeatInterval)
}

// 播放通知声音
const playNotificationSound = () => {
  try {
    const audio = new Audio('/static/sounds/notification.mp3')
    audio.volume = 0.5
    audio.play().catch(error => {
      console.warn('播放通知声音失败:', error)
    })
  } catch (error) {
    console.warn('创建音频对象失败:', error)
  }
}

// 断开连接
const disconnect = () => {
  if (eventSource.value) {
    eventSource.value.close()
    eventSource.value = null
  }
  
  if (reconnectInterval.value) {
    clearTimeout(reconnectInterval.value)
    reconnectInterval.value = null
  }
  
  if (heartbeatInterval.value) {
    clearInterval(heartbeatInterval.value)
    heartbeatInterval.value = null
  }
  
  isConnected.value = false
  console.log('通知连接已断开')
}

// 格式化时间
const formatTime = (time) => {
  return new Date(time).toLocaleString()
}

// 启动连接监控
const startConnectionMonitor = () => {
  connectionCheckInterval.value = setInterval(() => {
    console.log('🔔 检查连接状态...')

    // 检查心跳超时
    const now = Date.now()
    if (isConnected.value && (now - lastHeartbeat.value) > config.heartbeatTimeout) {
      console.warn('🔔 心跳超时，重新连接...')
      connectToNotificationService()
    }

    // 检查SSE连接状态
    if (eventSource.value && eventSource.value.readyState === EventSource.CLOSED) {
      console.warn('🔔 SSE连接已关闭，重新连接...')
      connectToNotificationService()
    }
  }, config.connectionCheckInterval)
}

// 停止连接监控
const stopConnectionMonitor = () => {
  if (connectionCheckInterval.value) {
    clearInterval(connectionCheckInterval.value)
    connectionCheckInterval.value = null
  }
}

// 组件挂载
onMounted(() => {
  console.log('🔔 ReliableNotification组件已挂载')
  connectToNotificationService()
  startConnectionMonitor()
})

// 组件卸载
onBeforeUnmount(() => {
  console.log('🔔 ReliableNotification组件即将卸载')
  disconnect()
  stopConnectionMonitor()
})

// 暴露方法给父组件
defineExpose({
  connect: connectToNotificationService,
  disconnect,
  isConnected: () => isConnected.value,
  reconnect: () => {
    disconnect()
    setTimeout(connectToNotificationService, 1000)
  }
})
</script>

<style scoped>
.reliable-notification {
  position: relative;
}

.connection-status {
  position: fixed;
  top: 60px;
  right: 20px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 2000;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.status-connected {
  background-color: #f0f9ff;
  color: #10b981;
  border: 1px solid #10b981;
}

.status-disconnected {
  background-color: #fef2f2;
  color: #ef4444;
  border: 1px solid #ef4444;
}

.status-reconnecting {
  background-color: #fffbeb;
  color: #f59e0b;
  border: 1px solid #f59e0b;
}

.offline-notifications {
  max-height: 400px;
  overflow-y: auto;
}

.notification-list {
  margin-top: 16px;
}

.notification-item {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #f9fafb;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notification-title {
  font-weight: 500;
  color: #374151;
}

.notification-time {
  font-size: 12px;
  color: #6b7280;
}

.notification-content {
  color: #4b5563;
  font-size: 14px;
  line-height: 1.4;
}
</style>
